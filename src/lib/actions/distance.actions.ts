"use server";

import { revalidatePath } from "next/cache";
import { createLogger } from "@/lib/utils/logger/logger";
import { ActionResponse } from "@/lib/schemas";
import {
  calculateDistance,
  batchCalculateDistances,
  optimizedBatchCalculateDistances,
} from "@/lib/services/google-maps/distance-calculation.service";
import {
  validateRouteCoordinates,
  validateDistanceResult,
  estimateDistanceCalculationCost,
} from "@/lib/services/google-maps/distance-calculation.utils";
import type {
  DistanceCalculationResult,
  RouteCoordinates,
  DistanceBatchInput,
  BatchDistanceCalculationResult,
} from "@/lib/services/google-maps/types";

const logger = createLogger("DistanceActions");

/**
 * Server Action for manual distance calculation
 * Calculates distance for a single route using Google Maps Distance Matrix API
 */
export async function calculateDistanceAction(
  route: RouteCoordinates,
  options?: {
    mode?: "driving" | "walking" | "bicycling" | "transit";
    units?: "metric" | "imperial";
    avoidTolls?: boolean;
    avoidHighways?: boolean;
    avoidFerries?: boolean;
  },
): Promise<ActionResponse<DistanceCalculationResult>> {
  try {
    logger.info(`Calculating distance for route: ${route.origin.lat},${route.origin.lng} to ${route.destination.lat},${route.destination.lng}`);

    // Validate route coordinates
    if (!validateRouteCoordinates(route)) {
      return {
        success: false,
        error: "Invalid route coordinates provided",
        fieldErrors: {
          route: ["Origin and destination coordinates must be valid latitude/longitude values"]
        },
      };
    }

    const result = await calculateDistance(route, options);

    if (result.success) {
      logger.info(`Successfully calculated distance: ${result.distanceKm}km`);
    } else {
      logger.warn(`Failed to calculate distance: ${result.error}`);
    }

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in calculateDistanceAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to calculate distance",
    };
  }
}

/**
 * Server Action for batch distance calculation
 * Calculates distances for multiple routes concurrently
 */
export async function batchCalculateDistancesAction(
  routes: DistanceBatchInput[],
  options?: {
    concurrency?: number;
    batchDelay?: number;
    mode?: "driving" | "walking" | "bicycling" | "transit";
    units?: "metric" | "imperial";
    avoidTolls?: boolean;
    avoidHighways?: boolean;
    avoidFerries?: boolean;
  },
): Promise<ActionResponse<BatchDistanceCalculationResult>> {
  try {
    logger.info(`Starting batch distance calculation for ${routes.length} routes`);

    if (!routes || routes.length === 0) {
      return {
        success: false,
        error: "At least one route is required for batch distance calculation",
        fieldErrors: { routes: ["Routes array cannot be empty"] },
      };
    }

    // Validate input routes
    for (let i = 0; i < routes.length; i++) {
      const routeInput = routes[i];
      if (!routeInput.id || !validateRouteCoordinates(routeInput.route)) {
        return {
          success: false,
          error: `Invalid route at index ${i}: ID and valid coordinates are required`,
          fieldErrors: { [`routes.${i}`]: ["ID and valid route coordinates are required"] },
        };
      }
    }

    const result = await batchCalculateDistances(routes, options);

    logger.info(
      `Batch distance calculation completed: ${result.successfulCalculations}/${result.totalProcessed} successful`,
    );

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in batchCalculateDistancesAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to calculate distances in batch",
    };
  }
}

/**
 * Server Action for optimized batch distance calculation
 * Uses Distance Matrix API's native batch capability for better performance
 */
export async function optimizedBatchCalculateDistancesAction(
  routes: DistanceBatchInput[],
  options?: {
    batchSize?: number;
    requestDelay?: number;
    mode?: "driving" | "walking" | "bicycling" | "transit";
    units?: "metric" | "imperial";
    avoidTolls?: boolean;
    avoidHighways?: boolean;
    avoidFerries?: boolean;
  },
): Promise<ActionResponse<BatchDistanceCalculationResult>> {
  try {
    logger.info(`Starting optimized batch distance calculation for ${routes.length} routes`);

    if (!routes || routes.length === 0) {
      return {
        success: false,
        error: "At least one route is required for optimized batch distance calculation",
        fieldErrors: { routes: ["Routes array cannot be empty"] },
      };
    }

    // Validate input routes
    for (let i = 0; i < routes.length; i++) {
      const routeInput = routes[i];
      if (!routeInput.id || !validateRouteCoordinates(routeInput.route)) {
        return {
          success: false,
          error: `Invalid route at index ${i}: ID and valid coordinates are required`,
          fieldErrors: { [`routes.${i}`]: ["ID and valid route coordinates are required"] },
        };
      }
    }

    const result = await optimizedBatchCalculateDistances(routes, options);

    logger.info(
      `Optimized batch distance calculation completed: ${result.successfulCalculations}/${result.totalProcessed} successful`,
    );

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in optimizedBatchCalculateDistancesAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to calculate distances in optimized batch",
    };
  }
}

/**
 * Server Action for route coordinate validation
 * Validates route coordinates before distance calculation
 */
export async function validateRouteCoordinatesAction(
  route: RouteCoordinates,
): Promise<ActionResponse<boolean>> {
  try {
    logger.debug(`Validating route coordinates`);

    const isValid = validateRouteCoordinates(route);

    logger.debug(`Route validation result: ${isValid ? "valid" : "invalid"}`);

    return { success: true, data: isValid };
  } catch (error) {
    logger.error("Error in validateRouteCoordinatesAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to validate route coordinates",
    };
  }
}

/**
 * Server Action for distance result validation
 * Validates distance calculation results
 */
export async function validateDistanceResultAction(
  result: DistanceCalculationResult,
): Promise<ActionResponse<boolean>> {
  try {
    logger.debug("Validating distance calculation result");

    const isValid = validateDistanceResult(result);

    logger.debug(`Distance result validation: ${isValid ? "valid" : "invalid"}`);

    return { success: true, data: isValid };
  } catch (error) {
    logger.error("Error in validateDistanceResultAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to validate distance result",
    };
  }
}

/**
 * Server Action for cost estimation
 * Estimates the cost of distance calculations based on Google Maps pricing
 */
export async function estimateDistanceCalculationCostAction(
  numberOfRoutes: number,
): Promise<ActionResponse<number>> {
  try {
    logger.debug(`Estimating cost for ${numberOfRoutes} distance calculations`);

    if (numberOfRoutes < 0) {
      return {
        success: false,
        error: "Number of routes must be non-negative",
        fieldErrors: { numberOfRoutes: ["Must be a non-negative number"] },
      };
    }

    const estimatedCost = estimateDistanceCalculationCost(numberOfRoutes);

    logger.debug(`Estimated cost: $${estimatedCost.toFixed(2)}`);

    return { success: true, data: estimatedCost };
  } catch (error) {
    logger.error("Error in estimateDistanceCalculationCostAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to estimate calculation cost",
    };
  }
}

/**
 * Server Action for distance calculation with form data
 * Calculates distance from form input with validation
 */
export async function calculateDistanceFromFormAction(
  formData: FormData,
): Promise<ActionResponse<DistanceCalculationResult>> {
  try {
    const originLat = parseFloat(formData.get("originLat") as string);
    const originLng = parseFloat(formData.get("originLng") as string);
    const destinationLat = parseFloat(formData.get("destinationLat") as string);
    const destinationLng = parseFloat(formData.get("destinationLng") as string);
    const mode = (formData.get("mode") as string) || "driving";
    const units = (formData.get("units") as string) || "metric";

    logger.info(`Calculating distance from form: ${originLat},${originLng} to ${destinationLat},${destinationLng}`);

    // Validate coordinates
    if (isNaN(originLat) || isNaN(originLng) || isNaN(destinationLat) || isNaN(destinationLng)) {
      return {
        success: false,
        error: "All coordinates must be valid numbers",
        fieldErrors: {
          coordinates: ["Origin and destination coordinates must be valid numbers"],
        },
      };
    }

    const route: RouteCoordinates = {
      origin: { lat: originLat, lng: originLng },
      destination: { lat: destinationLat, lng: destinationLng },
    };

    if (!validateRouteCoordinates(route)) {
      return {
        success: false,
        error: "Invalid coordinate values provided",
        fieldErrors: {
          coordinates: ["Coordinates must be within valid latitude/longitude ranges"],
        },
      };
    }

    const options = {
      mode: mode as "driving" | "walking" | "bicycling" | "transit",
      units: units as "metric" | "imperial",
    };

    const result = await calculateDistance(route, options);

    // Revalidate any pages that might display distance data
    revalidatePath("/dashboard/rfqs");

    return { success: true, data: result };
  } catch (error) {
    logger.error("Error in calculateDistanceFromFormAction:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to calculate distance from form",
    };
  }
}
