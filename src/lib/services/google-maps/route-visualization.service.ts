"use server";

import { createLogger } from "@/lib/utils/logger/logger";
import { retry } from "@/lib/utils/retry";
import {
  RouteCoordinates,
  RouteVisualizationResult,
  RouteVisualizationOptions,
  RouteBounds,
  RouteLeg,
} from "./types";
import {
  validateRouteCoordinates,
  formatDistance,
  formatDuration,
} from "./route-visualization.utils";

const logger = createLogger("RouteVisualizationService");

const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!;

if (!GOOGLE_MAPS_API_KEY) {
  throw new Error("NEXT_PUBLIC_GOOGLE_MAPS_API_KEY is not defined");
}

/**
 * Calculates route visualization data using Google Routes API
 * @param route Route coordinates (origin and destination)
 * @param options Route calculation options
 * @returns Promise resolving to route visualization result
 */
export async function calculateRouteVisualization(
  route: RouteCoordinates,
  options: RouteVisualizationOptions = {},
): Promise<RouteVisualizationResult> {
  try {
    // Validate coordinates before making API call
    if (!validateRouteCoordinates(route)) {
      return {
        success: false,
        error: "Invalid coordinates provided for route calculation",
      };
    }

    logger.info("Calculating route visualization", {
      origin: route.origin,
      destination: route.destination,
      options,
    });

    const {
      travelMode = "DRIVE",
      routePreferences = {},
      includeAlternatives = false,
      language = "en",
      units = "METRIC",
    } = options;

    const result = await retry(
      async () => {
        // Use Google Routes API (newer than Directions API)
        const url = "https://routes.googleapis.com/directions/v2:computeRoutes";

        const requestBody = {
          origin: {
            location: {
              latLng: {
                latitude: route.origin.lat,
                longitude: route.origin.lng,
              },
            },
          },
          destination: {
            location: {
              latLng: {
                latitude: route.destination.lat,
                longitude: route.destination.lng,
              },
            },
          },
          travelMode,
          routingPreference: "TRAFFIC_AWARE",
          computeAlternativeRoutes: includeAlternatives,
          routeModifiers: {
            avoidTolls: routePreferences.avoidTolls || false,
            avoidHighways: routePreferences.avoidHighways || false,
            avoidFerries: routePreferences.avoidFerries || false,
          },
          languageCode: language,
          units: units.toLowerCase(),
        };

        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Goog-Api-Key": GOOGLE_MAPS_API_KEY,
            "X-Goog-FieldMask":
              "routes.duration,routes.distanceMeters,routes.polyline.encodedPolyline," +
              "routes.legs.duration,routes.legs.distanceMeters,routes.legs.startLocation," +
              "routes.legs.endLocation,routes.viewport",
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Routes API error: ${response.status} - ${errorText}`);
        }

        return await response.json();
      },
      {
        maxRetries: 3,
        initialDelay: 1000,
        shouldRetry: (error) => {
          // Retry on network errors and server errors, but not on client errors
          if (error instanceof Error && error.message.includes("Routes API error:")) {
            const statusMatch = error.message.match(/(\d{3})/);
            if (statusMatch) {
              const status = parseInt(statusMatch[1]);
              return status === 429 || status >= 500;
            }
          }
          return true; // Retry on network errors
        },
        onRetry: (error, attempt) => {
          logger.warn(
            `Retrying route visualization (attempt ${attempt}) for route: ${route.origin.lat},${route.origin.lng} to ${route.destination.lat},${route.destination.lng}`,
            error,
          );
        },
      },
    );

    // Process the API response
    if (!result.routes || result.routes.length === 0) {
      return {
        success: false,
        error: "No routes found between the specified locations",
        status: "ZERO_RESULTS",
      };
    }

    const primaryRoute = result.routes[0];

    // Extract polyline
    const polyline = primaryRoute.polyline?.encodedPolyline;
    if (!polyline) {
      return {
        success: false,
        error: "No polyline data received from Routes API",
      };
    }

    // Extract bounds from viewport
    let bounds: RouteBounds | undefined;
    if (primaryRoute.viewport) {
      bounds = {
        northeast: {
          lat: primaryRoute.viewport.high.latitude,
          lng: primaryRoute.viewport.high.longitude,
        },
        southwest: {
          lat: primaryRoute.viewport.low.latitude,
          lng: primaryRoute.viewport.low.longitude,
        },
      };
    }

    // Extract legs information
    const legs: RouteLeg[] = primaryRoute.legs?.map((leg: any) => ({
      startLocation: {
        lat: leg.startLocation.latLng.latitude,
        lng: leg.startLocation.latLng.longitude,
      },
      endLocation: {
        lat: leg.endLocation.latLng.latitude,
        lng: leg.endLocation.latLng.longitude,
      },
      distanceMeters: leg.distanceMeters || 0,
      durationSeconds: parseInt(leg.duration?.replace('s', '') || '0'),
      distanceText: formatDistance(leg.distanceMeters || 0, units),
      durationText: formatDuration(parseInt(leg.duration?.replace('s', '') || '0')),
    })) || [];

    // Extract total distance and duration
    const distanceMeters = primaryRoute.distanceMeters || 0;
    const durationSeconds = parseInt(primaryRoute.duration?.replace('s', '') || '0');

    logger.info("Route visualization calculated successfully", {
      distanceMeters,
      durationSeconds,
      legsCount: legs.length,
    });

    return {
      success: true,
      polyline,
      bounds,
      legs,
      distanceMeters,
      durationSeconds,
      distanceText: formatDistance(distanceMeters, units),
      durationText: formatDuration(durationSeconds),
      status: "OK",
    };

  } catch (error) {
    logger.error("Error calculating route visualization:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}


