import { RouteCoordinates, DistanceCalculationResult } from "./types";
import { validateCoordinates } from "./coordinate-resolution.utils";

/**
 * Error class for distance calculation failures
 */
export class DistanceCalculationError extends Error {
  constructor(
    message: string,
    public status?: string,
    public code?: string,
  ) {
    super(message);
    this.name = "DistanceCalculationError";
  }
}

/**
 * Validates route coordinates before distance calculation
 * @param route Route coordinates to validate
 * @returns True if coordinates are valid
 */
export function validateRouteCoordinates(route: RouteCoordinates): boolean {
  return (
    validateCoordinates(route.origin.lat, route.origin.lng) &&
    validateCoordinates(route.destination.lat, route.destination.lng)
  );
}

/**
 * Estimates the cost of distance calculations based on Google Maps pricing
 * @param numberOfRoutes Number of routes to calculate
 * @returns Estimated cost in USD
 */
export function estimateDistanceCalculationCost(numberOfRoutes: number): number {
  // Google Distance Matrix API pricing (as of 2024)
  // $5.00 per 1000 elements for standard usage
  const costPer1000Elements = 5.0;
  return (numberOfRoutes / 1000) * costPer1000Elements;
}

/**
 * Validates distance calculation result
 * @param result Distance calculation result to validate
 * @returns True if result is valid
 */
export function validateDistanceResult(result: DistanceCalculationResult): boolean {
  return (
    result.success &&
    typeof result.distanceKm === "number" &&
    result.distanceKm > 0 &&
    typeof result.durationSeconds === "number" &&
    result.durationSeconds > 0
  );
}
