"use server";

import { createLogger } from "@/lib/utils/logger/logger";
import { retry } from "@/lib/utils/retry";
import {
  CoordinateResolutionResult,
  AddressBatchInput,
  BatchCoordinateResolutionResult,
} from "./types";
import {
  CoordinateResolutionError,
  normalizeAddress,
} from "./coordinate-resolution.utils";

const logger = createLogger("CoordinateResolutionService");

const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!;

if (!GOOGLE_MAPS_API_KEY) {
  throw new Error("NEXT_PUBLIC_GOOGLE_MAPS_API_KEY is not defined");
}

/**
 * Resolves coordinates for a single address using Google Maps Geocoding API
 * @param address The address to geocode
 * @param countryCode Optional country code for better accuracy
 * @returns Promise resolving to coordinate resolution result
 */
export async function resolveCoordinates(
  address: string,
  countryCode?: string,
): Promise<CoordinateResolutionResult> {
  try {
    const normalizedAddress = normalizeAddress(address, countryCode);

    logger.debug(`Resolving coordinates for address: ${normalizedAddress}`);

    const result = await retry(
      async () => {
        const url = new URL("https://maps.googleapis.com/maps/api/geocode/json");
        url.searchParams.set("address", normalizedAddress);
        url.searchParams.set("key", GOOGLE_MAPS_API_KEY);

        const response = await fetch(url.toString());

        if (!response.ok) {
          throw new CoordinateResolutionError(
            `HTTP error: ${response.status} ${response.statusText}`,
            response.status.toString(),
          );
        }

        return response.json();
      },
      {
        maxRetries: 3,
        initialDelay: 1000,
        shouldRetry: (error) => {
          // Retry on network errors and server errors, but not on client errors
          if (error instanceof CoordinateResolutionError) {
            const status = parseInt(error.status || "0");
            return status === 429 || status >= 500;
          }
          return true; // Retry on network errors
        },
        onRetry: (error, attempt) => {
          logger.warn(
            `Retrying coordinate resolution (attempt ${attempt}) for address: ${normalizedAddress}`,
            error,
          );
        },
      },
    );

    // Parse Google Maps Geocoding API response
    if (result.status !== "OK") {
      logger.warn(`Geocoding failed for address: ${normalizedAddress}, status: ${result.status}`);
      return {
        success: false,
        error: `Geocoding failed: ${result.status}`,
      };
    }

    if (!result.results || result.results.length === 0) {
      logger.warn(`No results found for address: ${normalizedAddress}`);
      return {
        success: false,
        error: "No results found for the provided address",
      };
    }

    const firstResult = result.results[0];
    const location = firstResult.geometry?.location;

    if (!location || typeof location.lat !== "number" || typeof location.lng !== "number") {
      logger.warn(`Invalid coordinates in result for address: ${normalizedAddress}`);
      return {
        success: false,
        error: "Invalid coordinates in geocoding result",
      };
    }

    // Map Google's location_type to our confidence levels
    const locationType = firstResult.geometry?.location_type;
    let confidence: CoordinateResolutionResult["confidence"];
    switch (locationType) {
      case "ROOFTOP":
        confidence = "ROOFTOP";
        break;
      case "RANGE_INTERPOLATED":
        confidence = "RANGE_INTERPOLATED";
        break;
      case "GEOMETRIC_CENTER":
        confidence = "GEOMETRIC_CENTER";
        break;
      default:
        confidence = "APPROXIMATE";
    }

    logger.info(`Successfully resolved coordinates for address: ${normalizedAddress}`);

    return {
      success: true,
      lat: location.lat,
      lng: location.lng,
      formattedAddress: firstResult.formatted_address,
      placeId: firstResult.place_id,
      confidence,
    };
  } catch (error) {
    logger.error(`Error resolving coordinates for address: ${address}`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Resolves coordinates for multiple addresses in batch
 * @param addresses Array of addresses to geocode
 * @param options Batch processing options
 * @returns Promise resolving to batch coordinate resolution result
 */
export async function batchResolveCoordinates(
  addresses: AddressBatchInput[],
  options: {
    /** Maximum number of concurrent requests */
    concurrency?: number;
    /** Delay between batches in milliseconds */
    batchDelay?: number;
  } = {},
): Promise<BatchCoordinateResolutionResult> {
  const { concurrency = 5, batchDelay = 100 } = options;

  logger.info(`Starting batch coordinate resolution for ${addresses.length} addresses`);

  const results: BatchCoordinateResolutionResult["results"] = [];
  let successfulResolutions = 0;

  // Process addresses in batches to respect API rate limits
  for (let i = 0; i < addresses.length; i += concurrency) {
    const batch = addresses.slice(i, i + concurrency);

    logger.debug(`Processing batch ${Math.floor(i / concurrency) + 1} with ${batch.length} addresses`);

    // Process current batch concurrently
    const batchPromises = batch.map(async (addressInput) => {
      const result = await resolveCoordinates(addressInput.address, addressInput.countryCode);

      if (result.success) {
        successfulResolutions++;
      }

      return {
        id: addressInput.id,
        result,
      };
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Add delay between batches to respect rate limits
    if (i + concurrency < addresses.length && batchDelay > 0) {
      await new Promise((resolve) => setTimeout(resolve, batchDelay));
    }
  }

  const successRate = addresses.length > 0 ? successfulResolutions / addresses.length : 0;

  logger.info(
    `Batch coordinate resolution completed: ${successfulResolutions}/${addresses.length} successful (${Math.round(successRate * 100)}%)`,
  );

  return {
    results,
    successRate,
    totalProcessed: addresses.length,
    successfulResolutions,
  };
}


