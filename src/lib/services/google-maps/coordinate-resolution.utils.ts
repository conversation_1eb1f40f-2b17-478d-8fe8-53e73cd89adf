import * as crypto from "crypto";
import { AddressChangeDetection, CoordinateResolutionResult, CachedCoordinates } from "./types";

/**
 * Error class for coordinate resolution failures
 */
export class CoordinateResolutionError extends Error {
  constructor(
    message: string,
    public status?: string,
    public code?: string,
  ) {
    super(message);
    this.name = "CoordinateResolutionError";
  }
}

/**
 * Generates a hash for address components to detect changes
 * @param addressComponents Object containing address fields
 * @returns SHA-256 hash of the address components
 */
export function generateAddressHash(addressComponents: {
  originCity?: string;
  originAddress?: string;
  originPostalCode?: string;
  destinationCity?: string;
  destinationAddress?: string;
  destinationPostalCode?: string;
}): string {
  const {
    originCity = "",
    originAddress = "",
    originPostalCode = "",
    destinationCity = "",
    destinationAddress = "",
    destinationPostalCode = "",
  } = addressComponents;

  // Create a normalized string from all address components
  const addressString = [
    originCity.trim().toLowerCase(),
    originAddress.trim().toLowerCase(),
    originPostalCode.trim().toLowerCase(),
    destinationCity.trim().toLowerCase(),
    destinationAddress.trim().toLowerCase(),
    destinationPostalCode.trim().toLowerCase(),
  ].join("|");

  return crypto.createHash("sha256").update(addressString).digest("hex");
}

/**
 * Detects if address components have changed by comparing hashes
 * @param currentAddressComponents Current address components
 * @param previousHash Previous address hash (if any)
 * @returns Address change detection result
 */
export function detectAddressChange(
  currentAddressComponents: {
    originCity?: string;
    originAddress?: string;
    originPostalCode?: string;
    destinationCity?: string;
    destinationAddress?: string;
    destinationPostalCode?: string;
  },
  previousHash?: string,
): AddressChangeDetection {
  const currentHash = generateAddressHash(currentAddressComponents);
  const hasChanged = previousHash ? currentHash !== previousHash : true;

  return {
    currentHash,
    previousHash,
    hasChanged,
    checkedAt: new Date(),
  };
}

/**
 * Normalizes an address string for consistent geocoding
 * @param address Raw address string
 * @param countryCode Optional country code for better accuracy
 * @returns Normalized address string
 */
export function normalizeAddress(address: string, countryCode?: string): string {
  // Basic normalization: trim whitespace and normalize spacing
  let normalized = address.trim().replace(/\s+/g, " ");

  // Add country code if provided and not already present
  if (countryCode && !normalized.toLowerCase().includes(countryCode.toLowerCase())) {
    normalized = `${normalized}, ${countryCode}`;
  }

  return normalized;
}

/**
 * Validates coordinate values
 * @param lat Latitude value
 * @param lng Longitude value
 * @returns True if coordinates are valid
 */
export function validateCoordinates(lat: number, lng: number): boolean {
  return (
    typeof lat === "number" &&
    typeof lng === "number" &&
    !isNaN(lat) &&
    !isNaN(lng) &&
    lat >= -90 &&
    lat <= 90 &&
    lng >= -180 &&
    lng <= 180
  );
}

/**
 * Creates cached coordinates object from resolution result
 * @param result Coordinate resolution result
 * @param addressHash Hash of the address used for resolution
 * @returns Cached coordinates object or null if resolution failed
 */
export function createCachedCoordinates(
  result: CoordinateResolutionResult,
  addressHash: string,
): CachedCoordinates | null {
  if (!result.success || result.lat === undefined || result.lng === undefined) {
    return null;
  }

  return {
    lat: result.lat,
    lng: result.lng,
    resolvedAt: new Date(),
    addressHash,
    formattedAddress: result.formattedAddress,
  };
}
