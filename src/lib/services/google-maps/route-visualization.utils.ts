import {
  RouteCoordinates,
  RouteVisualizationResult,
  RouteBounds,
  RouteMarker,
  RouteStyle,
  BoundsCalculationOptions,
} from "./types";
import { validateCoordinates } from "./coordinate-resolution.utils";

/**
 * Default route styling configuration
 */
export const DEFAULT_ROUTE_STYLE: RouteStyle = {
  strokeColor: "#2563eb", // Blue-600 from Tailwind
  strokeWeight: 4,
  strokeOpacity: 0.8,
  showDirectionArrows: true,
};

/**
 * Default bounds calculation options
 */
export const DEFAULT_BOUNDS_OPTIONS: BoundsCalculationOptions = {
  padding: 50,
  minZoom: 5,
  maxZoom: 18,
};

/**
 * Validates route coordinates before making API calls
 * @param route Route coordinates to validate
 * @returns Whether the coordinates are valid
 */
export function validateRouteCoordinates(route: RouteCoordinates): boolean {
  return (
    validateCoordinates(route.origin.lat, route.origin.lng) &&
    validateCoordinates(route.destination.lat, route.destination.lng)
  );
}

/**
 * Formats distance in meters to human-readable text
 * @param meters Distance in meters
 * @param units Unit system to use
 * @returns Formatted distance string
 */
export function formatDistance(meters: number, units: "METRIC" | "IMPERIAL" = "METRIC"): string {
  if (units === "IMPERIAL") {
    const miles = meters * 0.000621371;
    if (miles < 1) {
      const feet = meters * 3.28084;
      return `${Math.round(feet)} ft`;
    }
    return `${miles.toFixed(1)} mi`;
  } else {
    if (meters < 1000) {
      return `${Math.round(meters)} m`;
    }
    const km = meters / 1000;
    return `${km.toFixed(1)} km`;
  }
}

/**
 * Formats duration in seconds to human-readable text
 * @param seconds Duration in seconds
 * @returns Formatted duration string
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Generates route markers for origin and destination
 * @param route Route coordinates
 * @param options Marker customization options
 * @returns Array of route markers
 */
export function generateRouteMarkers(
  route: RouteCoordinates,
  options: {
    originTitle?: string;
    destinationTitle?: string;
    customIcons?: boolean;
  } = {},
): RouteMarker[] {
  const {
    originTitle = "Origin",
    destinationTitle = "Destination",
    customIcons = true,
  } = options;

  const markers: RouteMarker[] = [
    {
      position: route.origin,
      type: "origin",
      title: originTitle,
    },
    {
      position: route.destination,
      type: "destination",
      title: destinationTitle,
    },
  ];

  // Add custom icons if requested
  if (customIcons) {
    markers[0].icon = {
      url: "data:image/svg+xml;base64," + btoa(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="8" fill="#10b981" stroke="#ffffff" stroke-width="2"/>
          <circle cx="12" cy="12" r="3" fill="#ffffff"/>
        </svg>
      `),
      size: { width: 24, height: 24 },
      anchor: { x: 12, y: 12 },
    };

    markers[1].icon = {
      url: "data:image/svg+xml;base64," + btoa(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="8" fill="#ef4444" stroke="#ffffff" stroke-width="2"/>
          <circle cx="12" cy="12" r="3" fill="#ffffff"/>
        </svg>
      `),
      size: { width: 24, height: 24 },
      anchor: { x: 12, y: 12 },
    };
  }

  return markers;
}

/**
 * Calculates optimal map bounds for route visualization
 * @param route Route coordinates
 * @param options Bounds calculation options
 * @returns Calculated route bounds
 */
export function calculateOptimalBounds(
  route: RouteCoordinates,
  options: BoundsCalculationOptions = DEFAULT_BOUNDS_OPTIONS,
): RouteBounds {
  const { padding = 50 } = options;

  // Calculate the bounds that encompass both origin and destination
  const latitudes = [route.origin.lat, route.destination.lat];
  const longitudes = [route.origin.lng, route.destination.lng];

  const minLat = Math.min(...latitudes);
  const maxLat = Math.max(...latitudes);
  const minLng = Math.min(...longitudes);
  const maxLng = Math.max(...longitudes);

  // Add padding (convert pixels to approximate degrees)
  // This is a rough approximation - in a real implementation you'd want more precise calculations
  const latPadding = (padding / 111000) * 1.5; // ~111km per degree latitude
  const lngPadding = (padding / (111000 * Math.cos(minLat * Math.PI / 180))) * 1.5;

  return {
    southwest: {
      lat: minLat - latPadding,
      lng: minLng - lngPadding,
    },
    northeast: {
      lat: maxLat + latPadding,
      lng: maxLng + lngPadding,
    },
  };
}

/**
 * Estimates the cost of route visualization API calls
 * @param numberOfRoutes Number of routes to visualize
 * @returns Estimated cost in USD
 */
export function estimateRouteVisualizationCost(numberOfRoutes: number): number {
  // Google Routes API pricing (as of 2024)
  // $5.00 per 1000 requests for basic routing
  const costPer1000Requests = 5.0;
  return (numberOfRoutes / 1000) * costPer1000Requests;
}

/**
 * Validates route visualization result
 * @param result Route visualization result to validate
 * @returns Whether the result is valid and complete
 */
export function validateRouteVisualizationResult(
  result: RouteVisualizationResult,
): boolean {
  if (!result.success) {
    return false;
  }

  // Check required fields for successful result
  return !!(
    result.polyline &&
    result.distanceMeters !== undefined &&
    result.durationSeconds !== undefined &&
    result.legs &&
    result.legs.length > 0
  );
}
